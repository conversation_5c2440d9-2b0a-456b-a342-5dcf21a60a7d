using System.Collections.Generic;
using System.Linq;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;

        [SerializeField]
        private Transform _shapeContainer;

        [SerializeField]
        private Transform[] _spawnPoints;

        private readonly Shape[] _shapes = new Shape[3];

        public void Init()
        {
            LoadSavedShapes(DataShortcut.Board.savedShapes);
        }

        public void LoadSavedShapes(List<BoardData.Shape> savedShapes)
        {
            if (savedShapes == null || savedShapes.Count == 0)
            {
                SpawnAllShapes();
                return;
            }

            for (var i = 0; i < savedShapes.Count; i++)
            {
                if (savedShapes[i] == null) continue;
                _shapes[i] = SpawnShape(i, _shapeDefinition.shapes.First(t => t.id == savedShapes[i].id), savedShapes[i].blockTypes);
            }
        }

        [Button]
        public void SpawnAllShapes(bool showImmediately = false)
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;

                Core.ScenePool.Recycle(shape.gameObject);
            }

            var spawnableBlockTypes = Enumerable.Range(100, 3).OrderBy(t => Random.value).Take(2).ToList();
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                var shapeDatum = _shapeDefinition.shapes[Random.Range(0, _shapeDefinition.shapes.Count)];
                var blockType = (BlockType)spawnableBlockTypes[Random.Range(0, 2)];
                var blockTypesInShape = Enumerable.Repeat(blockType, shapeDatum.coordinates.Count).ToList();
                _shapes[i] = SpawnShape(i, shapeDatum, blockTypesInShape);
                if (showImmediately)
                {
                    _shapes[i].Show();
                }
            }
        }

        private Shape SpawnShape(int index, ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject, _shapeContainer).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].localPosition;
            shape.Init(datum, blockTypes);
            return shape;
        }

        public void ShowAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Show();
            }
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }

        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }

        public List<BoardData.Shape> GetSavedData()
        {
            return _shapes.Select(t => t == null ? null : new BoardData.Shape() { id = t.Id, blockTypes = t.BlockTypes }).ToList();
        }

        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                Despawn(shape);
            }
        }
    }
}