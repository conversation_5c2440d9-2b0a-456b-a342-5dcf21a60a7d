using System.Collections.Generic;
using System.Linq;
using OnePuz.Data;
using OnePuz.Services.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;

        [SerializeField]
        private Transform _shapeContainer;

        [SerializeField]
        private Transform[] _spawnPoints;

        private readonly Shape[] _shapes = new Shape[3];

        public void Init()
        {
            // Initialize the shape spawn rule system
            ShapeSpawnRule.Initialize();

            LoadSavedShapes(DataShortcut.Board.savedShapes);

            // Subscribe to map analysis events
            this.EventSubscribe<OnMapAnalysisCompletedEvent>(HandleMapAnalysisCompleted);

            // Subscribe to score level up events to update spawn rules
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);

            // Set initial level from current score
            var currentLevel = DataShortcut.Score.level;
            ShapeSpawnRule.SetLevel(currentLevel);
        }

        public void LoadSavedShapes(List<BoardData.Shape> savedShapes)
        {
            if (savedShapes == null || savedShapes.Count == 0)
            {
                SpawnAllShapes();
                return;
            }

            for (var i = 0; i < savedShapes.Count; i++)
            {
                if (savedShapes[i] == null) continue;
                _shapes[i] = SpawnShape(i, _shapeDefinition.shapes.First(t => t.id == savedShapes[i].id), savedShapes[i].blockTypes);
            }
        }

        [Button]
        public void SpawnAllShapes(bool showImmediately = false)
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;

                Core.ScenePool.Recycle(shape.gameObject);
            }

            // Use rule-based spawning instead of simple random
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                var shapeDatum = _shapeDefinition.shapes[Random.Range(0, _shapeDefinition.shapes.Count)];
                var spawnResult = ShapeSpawnRule.GenerateShapeBlockTypes(shapeDatum);

                _shapes[i] = SpawnShape(i, shapeDatum, spawnResult.blockTypes);
                if (showImmediately)
                {
                    _shapes[i].Show();
                }

                // Log spawn info for debugging
                OLogger.Log($"Spawned shape {i}: {spawnResult.debugInfo}");
            }
        }

        private Shape SpawnShape(int index, ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject, _shapeContainer).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].localPosition;
            shape.Init(datum, blockTypes);
            return shape;
        }

        public void ShowAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Show();
            }
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }

        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }

        public List<BoardData.Shape> GetSavedData()
        {
            return _shapes.Select(t => t == null ? null : new BoardData.Shape() { id = t.Id, blockTypes = t.BlockTypes }).ToList();
        }

        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                Despawn(shape);
            }

            // Reset the spawn rule system
            ShapeSpawnRule.Reset();
        }

        private void HandleMapAnalysisCompleted(OnMapAnalysisCompletedEvent e)
        {
            // Update spawn rules with new map analysis data
            ShapeSpawnRule.UpdateMapAnalysis(e.analysisResults);
            OLogger.Log("ShapeSpawner: Updated spawn rules with new map analysis data");
        }

        private void HandleScoreLevelUp(OnScoreLevelUpEvent e)
        {
            // Update spawn rules when level changes
            ShapeSpawnRule.SetLevel(e.level);
            OLogger.Log($"ShapeSpawner: Updated spawn rules to level {e.level}");
        }

        private void OnDestroy()
        {
            this.EventUnsubscribe<OnMapAnalysisCompletedEvent>(HandleMapAnalysisCompleted);
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
        }
    }
}