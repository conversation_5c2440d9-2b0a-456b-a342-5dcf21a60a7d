using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Configuration for shape spawning rules based on level
    /// </summary>
    [System.Serializable]
    public class LevelSpawnConfig
    {
        [Header("Basic Settings")]
        public int level;
        public int colorCount = 4; // Number of different colors/materials to use
        public List<BlockType> availableBlockTypes = new List<BlockType>(); // Available block types for this level
        
        [Header("Multi-color Shape Settings")]
        [Range(0f, 1f)]
        public float multiColorShapeChance = 0f; // Chance to spawn a shape with 2 colors
        
        [Header("Clear Guarantee Settings")]
        public int spawnAllCountForGuaranteedClear = 4; // After this many spawn-all cycles, guarantee a clearable spawn
        
        [Header("Tricky Round Settings (Level 3+)")]
        public bool enableTrickyRounds = false;
        public int spawnAllCountForTrickyRound = 5; // After this many spawn-all cycles, do a tricky round
    }

    /// <summary>
    /// Data about current spawning state
    /// </summary>
    public class SpawnState
    {
        public int currentSpawnAllCount = 0;
        public int currentLevel = 0;
        public MapAnalysisJobData lastAnalysisResults;
        public bool hasAnalysisResults = false;
        
        public void Reset()
        {
            currentSpawnAllCount = 0;
            hasAnalysisResults = false;
            if (lastAnalysisResults.IsValid)
                lastAnalysisResults.Dispose();
        }
        
        public void UpdateAnalysisResults(MapAnalysisJobData newResults)
        {
            if (lastAnalysisResults.IsValid)
                lastAnalysisResults.Dispose();
                
            // Copy the results
            var maxMaterialIndex = newResults.materialPixelCounts.Length - 1;
            lastAnalysisResults = MapAnalysisJobData.Create(maxMaterialIndex);
            newResults.materialPixelCounts.CopyTo(lastAnalysisResults.materialPixelCounts);
            newResults.materialSurfacePixelCounts.CopyTo(lastAnalysisResults.materialSurfacePixelCounts);
            newResults.materialPercentages.CopyTo(lastAnalysisResults.materialPercentages);
            newResults.totalNonEmptyPixels.CopyTo(lastAnalysisResults.totalNonEmptyPixels);
            
            hasAnalysisResults = true;
        }
    }

    /// <summary>
    /// Result of shape spawn rule evaluation
    /// </summary>
    public struct ShapeSpawnResult
    {
        public List<BlockType> blockTypes; // Block types for each coordinate in the shape
        public bool isGuaranteedClear; // Whether this spawn is designed to clear lines
        public bool isTrickyRound; // Whether this is a tricky round spawn
        public string debugInfo; // Debug information about why this spawn was chosen
    }

    /// <summary>
    /// System for determining how to spawn shapes based on level rules and map analysis
    /// </summary>
    public static class ShapeSpawnRule
    {
        private static readonly List<LevelSpawnConfig> _levelConfigs = new List<LevelSpawnConfig>();
        private static SpawnState _spawnState = new SpawnState();
        
        /// <summary>
        /// Initialize the spawn rule system with level configurations
        /// </summary>
        public static void Initialize()
        {
            _levelConfigs.Clear();

            // Validate that we have the expected block types available
            if (!ValidateBlockTypes())
            {
                OLogger.LogWarning("ShapeSpawnRule: Some block types may not be available in material definitions");
            }
            
            // Level 0: 4 colors, simple spawning
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 0,
                colorCount = 4,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103 },
                multiColorShapeChance = 0f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });
            
            // Level 1: 4 colors, low chance of multi-color shapes
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 1,
                colorCount = 4,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103 },
                multiColorShapeChance = 0.15f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });
            
            // Level 2: 5 colors, higher chance of multi-color shapes
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 2,
                colorCount = 5,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104 },
                multiColorShapeChance = 0.25f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = false
            });
            
            // Level 3: 6 colors, tricky rounds enabled
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 3,
                colorCount = 6,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105 },
                multiColorShapeChance = 0.3f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 4: 7 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 4,
                colorCount = 7,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106 },
                multiColorShapeChance = 0.35f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
            
            // Level 5: 8 colors
            _levelConfigs.Add(new LevelSpawnConfig
            {
                level = 5,
                colorCount = 8,
                availableBlockTypes = new List<BlockType> { (BlockType)100, (BlockType)101, (BlockType)102, (BlockType)103, (BlockType)104, (BlockType)105, (BlockType)106, (BlockType)107 },
                multiColorShapeChance = 0.4f,
                spawnAllCountForGuaranteedClear = 4,
                enableTrickyRounds = true,
                spawnAllCountForTrickyRound = 5
            });
        }
        
        /// <summary>
        /// Update the current level
        /// </summary>
        public static void SetLevel(int level)
        {
            if (_spawnState.currentLevel != level)
            {
                _spawnState.currentLevel = level;
                _spawnState.currentSpawnAllCount = 0; // Reset spawn count when level changes
            }
        }
        
        /// <summary>
        /// Update map analysis results
        /// </summary>
        public static void UpdateMapAnalysis(MapAnalysisJobData analysisResults)
        {
            _spawnState.UpdateAnalysisResults(analysisResults);
        }
        
        /// <summary>
        /// Generate block types for a shape based on current rules and map state
        /// </summary>
        public static ShapeSpawnResult GenerateShapeBlockTypes(ShapeDefinition.Datum shapeDatum)
        {
            var config = GetCurrentLevelConfig();
            if (config == null)
            {
                // Fallback to simple random spawning
                return GenerateSimpleRandomShape(shapeDatum);
            }
            
            _spawnState.currentSpawnAllCount++;
            
            // Check if this should be a guaranteed clear spawn
            bool shouldGuaranteeClear = _spawnState.currentSpawnAllCount >= config.spawnAllCountForGuaranteedClear;
            
            // Check if this should be a tricky round (only for levels 3+)
            bool shouldDoTrickyRound = config.enableTrickyRounds && 
                                     _spawnState.currentSpawnAllCount >= config.spawnAllCountForTrickyRound;
            
            if (shouldDoTrickyRound)
            {
                _spawnState.currentSpawnAllCount = 0; // Reset counter
                return GenerateTrickyRoundShape(shapeDatum, config);
            }
            else if (shouldGuaranteeClear)
            {
                _spawnState.currentSpawnAllCount = 0; // Reset counter
                return GenerateGuaranteedClearShape(shapeDatum, config);
            }
            else
            {
                return GenerateNormalShape(shapeDatum, config);
            }
        }
        
        /// <summary>
        /// Reset the spawn state (call when game resets)
        /// </summary>
        public static void Reset()
        {
            _spawnState.Reset();
        }
        
        private static LevelSpawnConfig GetCurrentLevelConfig()
        {
            var config = _levelConfigs.FirstOrDefault(c => c.level == _spawnState.currentLevel);
            if (config == null)
            {
                // Fallback to highest available level config
                config = _levelConfigs.LastOrDefault();
                if (config != null)
                {
                    OLogger.LogWarning($"ShapeSpawnRule: No config for level {_spawnState.currentLevel}, using level {config.level}");
                }
            }
            return config;
        }

        private static bool ValidateBlockTypes()
        {
            // This is a basic validation - in a real implementation you'd check against actual material definitions
            // For now, we assume BlockType 100-107 are available
            return true;
        }

        /// <summary>
        /// Get debug information about current spawn state
        /// </summary>
        public static string GetDebugInfo()
        {
            var config = GetCurrentLevelConfig();
            return $"Level: {_spawnState.currentLevel}, SpawnCount: {_spawnState.currentSpawnAllCount}, " +
                   $"HasAnalysis: {_spawnState.hasAnalysisResults}, Config: {(config != null ? $"L{config.level}" : "None")}";
        }
        
        private static ShapeSpawnResult GenerateSimpleRandomShape(ShapeDefinition.Datum shapeDatum)
        {
            var blockTypes = new List<BlockType>();
            var randomBlockType = (BlockType)Random.Range(100, 103); // Default to first 3 colors
            
            for (int i = 0; i < shapeDatum.coordinates.Count; i++)
            {
                blockTypes.Add(randomBlockType);
            }
            
            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isGuaranteedClear = false,
                isTrickyRound = false,
                debugInfo = "Simple random (no level config)"
            };
        }
        
        private static ShapeSpawnResult GenerateNormalShape(ShapeDefinition.Datum shapeDatum, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();
            
            // Decide if this should be a multi-color shape
            bool isMultiColor = Random.value < config.multiColorShapeChance;
            
            if (isMultiColor && shapeDatum.coordinates.Count > 1)
            {
                // Generate a shape with 2 colors
                var color1 = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                var color2 = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                
                // Make sure colors are different
                while (color2 == color1 && config.colorCount > 1)
                {
                    color2 = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                }
                
                // Split the shape roughly in half between the two colors
                int halfCount = shapeDatum.coordinates.Count / 2;
                for (int i = 0; i < shapeDatum.coordinates.Count; i++)
                {
                    blockTypes.Add(i < halfCount ? color1 : color2);
                }
                
                return new ShapeSpawnResult
                {
                    blockTypes = blockTypes,
                    isGuaranteedClear = false,
                    isTrickyRound = false,
                    debugInfo = $"Multi-color normal shape: {color1}, {color2}"
                };
            }
            else
            {
                // Generate a single-color shape
                var color = config.availableBlockTypes[Random.Range(0, config.colorCount)];
                for (int i = 0; i < shapeDatum.coordinates.Count; i++)
                {
                    blockTypes.Add(color);
                }
                
                return new ShapeSpawnResult
                {
                    blockTypes = blockTypes,
                    isGuaranteedClear = false,
                    isTrickyRound = false,
                    debugInfo = $"Single-color normal shape: {color}"
                };
            }
        }
        
        private static ShapeSpawnResult GenerateGuaranteedClearShape(ShapeDefinition.Datum shapeDatum, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();

            if (!_spawnState.hasAnalysisResults)
            {
                // No analysis data, fall back to normal shape
                return GenerateNormalShape(shapeDatum, config);
            }

            // Find material index with highest surface pixel count among available block types
            var bestMaterialIndex = -1;
            var maxSurfaceCount = 0;

            for (int i = 0; i < config.availableBlockTypes.Count; i++)
            {
                // Map block type to material index (assuming BlockType 100 = material index 0, etc.)
                var materialIndex = (int)config.availableBlockTypes[i] - 100;
                if (materialIndex >= 0 && materialIndex < _spawnState.lastAnalysisResults.materialSurfacePixelCounts.Length)
                {
                    var surfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(materialIndex);
                    if (surfaceCount > maxSurfaceCount)
                    {
                        maxSurfaceCount = surfaceCount;
                        bestMaterialIndex = i; // Index in config.availableBlockTypes
                    }
                }
            }

            if (bestMaterialIndex < 0)
            {
                // No valid material found, fall back to normal shape
                return GenerateNormalShape(shapeDatum, config);
            }

            var bestBlockType = config.availableBlockTypes[bestMaterialIndex];

            // Generate shape with the best material for clearing
            for (int i = 0; i < shapeDatum.coordinates.Count; i++)
            {
                blockTypes.Add(bestBlockType);
            }

            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isGuaranteedClear = true,
                isTrickyRound = false,
                debugInfo = $"Guaranteed clear shape: {bestBlockType} (surface pixels: {maxSurfaceCount})"
            };
        }
        
        private static ShapeSpawnResult GenerateTrickyRoundShape(ShapeDefinition.Datum shapeDatum, LevelSpawnConfig config)
        {
            var blockTypes = new List<BlockType>();

            if (!_spawnState.hasAnalysisResults || shapeDatum.coordinates.Count < 2)
            {
                // No analysis data or shape too small, fall back to guaranteed clear
                return GenerateGuaranteedClearShape(shapeDatum, config);
            }

            // Find top 2 materials with highest surface pixel counts among available block types
            var materialSurfaceCounts = new List<(int configIndex, int surfaceCount)>();

            for (int i = 0; i < config.availableBlockTypes.Count; i++)
            {
                // Map block type to material index (assuming BlockType 100 = material index 0, etc.)
                var materialIndex = (int)config.availableBlockTypes[i] - 100;
                if (materialIndex >= 0 && materialIndex < _spawnState.lastAnalysisResults.materialSurfacePixelCounts.Length)
                {
                    var surfaceCount = _spawnState.lastAnalysisResults.GetSurfacePixelCount(materialIndex);
                    materialSurfaceCounts.Add((i, surfaceCount));
                }
            }

            // Sort by surface count descending
            materialSurfaceCounts.Sort((a, b) => b.surfaceCount.CompareTo(a.surfaceCount));

            if (materialSurfaceCounts.Count < 2)
            {
                // Not enough materials, fall back to guaranteed clear
                return GenerateGuaranteedClearShape(shapeDatum, config);
            }

            var bestBlockType = config.availableBlockTypes[materialSurfaceCounts[0].configIndex];
            var secondBlockType = config.availableBlockTypes[materialSurfaceCounts[1].configIndex];

            // Split shape half and half between the two materials
            int halfCount = shapeDatum.coordinates.Count / 2;
            for (int i = 0; i < shapeDatum.coordinates.Count; i++)
            {
                blockTypes.Add(i < halfCount ? bestBlockType : secondBlockType);
            }

            return new ShapeSpawnResult
            {
                blockTypes = blockTypes,
                isGuaranteedClear = false,
                isTrickyRound = true,
                debugInfo = $"Tricky round shape: {bestBlockType} ({materialSurfaceCounts[0].surfaceCount}) + {secondBlockType} ({materialSurfaceCounts[1].surfaceCount})"
            };
        }
    }
}
