Cần job chạy liên tục, handle 1 số đầu việc sau:
- Check toàn bộ map với các pixel khác empty, đưa ra tỷ lệ, mỗi materialIndex chiếm bao nhiêu % tổng lượng pixel. MaterialIndex ở đây là index của material trong material definition của Pixel
- Check toàn bộ materialIndex có pixel nằm ở các hàng trên cùng (trên pixel đó là empty), đưa ra được số pixel đã phủ bề mặt của từng materialIndex đó

Có thể tham khảo LoseCheckJob hoặc FloodFillJob để hiểu cách hoạt động hiện tại

Sau đó tôi muốn build rule sinh Shape cho ShapeSpawner dựa vào những data nhận được từ Job mới tạo.
Rule sẽ có config cho từng levels. 
- Level 0 quy định 4 màu, mỗi lần sinh shape sẽ ngẫu nhiên trong 4 màu đó. Cứ sau 4 lần spawn all, sẽ là 1 lần spawn sao cho shape chắc chắn clear được hàng. Tức là sẽ cần chọn 1 materialIndex có số pixel phủ bề mặt cao, spawn từ 1 tới 2 shape có cùng materialIndex đó. 
- Level 1 vẫn quy định 4 màu. Nhưng thêm logic 2 màu trong cùng 1 shape. Có 1 tỷ lệ ngẫu nhiên sinh 1 shape có chứa 2 màu, nhưng tỷ lệ này sẽ thấp thôi. Tương tự level 0, cứ 4 lần spawn all, sẽ lại có 1 lần spawn chắc chắn clear được hàng.
- Level 2 quy định 5 màu. Logic tương tự level 1. Tỉ lệ spawn shape có 2 màu sẽ tăng lên chút.
- Level 3 quy định 6 màu. Logic tương tự level 2. Sẽ có 1 setup cho tricky round, tầm 5 lần spawn all, sẽ tìm ra 1 materialIndex có pixel phủ bề mặt 1 nửa trên board, spawn 2 shape có 1 nửa là materialIndex đó, 1 nửa là materialIndex khác.
- Level 4 quy định 7 màu. Logic tương tự level 3. Tỉ lệ spawn shape có 2 màu sẽ tăng lên chút.
- Level 5 quy định 8 màu. Logic tương tự level 4. Tỉ lệ spawn shape có 2 màu sẽ tăng lên chút.